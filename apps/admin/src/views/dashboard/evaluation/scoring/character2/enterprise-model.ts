// 企业定制模型相关类型定义

// 项目选项
export interface ProjectOption {
    encryptId: string;
    name: string;
}

// 维度信息
export interface DimensionInfo {
    encryptId: string;
    name: string;
}

// 行数据项
export interface RowDataItem {
    encId?: string;
    encDimensionId: string;
    dimensionName: string;
    define: string;
    weight: number;
    normalAverageScore: number;
    normalStandardDeviation: number;
}

// 关键潜在素质列表项
export interface KeyPotentialQualityItem {
    encId?: string;
    headName: string;
    rowDataList: RowDataItem[];
}

// 岗位素质模型匹配列表项
export interface PositionQualityModelMatchItem {
    encId?: string;
    headName: string;
    rowDataList: RowDataItem[];
}

// 企业定制模型详细信息
export interface EnterpriseModelDetail {
    encId?: string;
    encCorpId: string;
    corpName: string;
    name: string;
    status: number;
    keyPotentialQualityList: KeyPotentialQualityItem[];
    positionQualityModelMatchList: PositionQualityModelMatchItem[];
}

// 模型保存参数
export interface ModelSaveParams {
    encCorpId: string;
    productId: number;
    encId?: string;
    name: string;
    keyPotentialQualityList: KeyPotentialQualityItem[];
    positionQualityModelMatchList: PositionQualityModelMatchItem[];
}

// 模型详情查询参数
export interface ModelDetailParams {
    encCorpId: string;
    productId: number;
    encId?: string;
}

// 表格列配置
export interface TableColumn {
    label: string;
    field: string;
    width?: number;
    editable?: boolean;
    type?: 'input' | 'number' | 'text';
    precision?: number;
    min?: number;
    max?: number;
    required?: boolean;
}

// 表单验证规则
export interface ValidationRule {
    required?: boolean;
    type?: string;
    message?: string;
    validator?: (value: any, callback: (message?: string) => void) => void;
    min?: number;
    max?: number;
}

// 配置表格数据
export interface ConfigTableData {
    headers: string[];
    dimensions: string[];
    data: Record<string, Record<string, RowDataItem>>;
}
