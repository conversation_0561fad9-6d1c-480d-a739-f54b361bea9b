/**
 * 五维性格测评 2.0 状态管理
 */

import { defineStore } from 'pinia';
import { ref, computed, reactive } from 'vue';
import type { Character2BaseParams, ApiConfigItem, DimensionItem, FormState, WeightValidationState } from './type';
import { CHARACTER2_CONSTANTS } from './type';
import { getDimensionQuery, getScoreParamDetail, saveScoreParam, validateWeightSum, handleApiError } from './api';

export const useCharacter2Store = defineStore('character2', () => {
    // 基础状态
    const baseParams = ref<Character2BaseParams>({
        paramA: undefined as any,
        paramB: undefined as any,
        scoreMin: undefined as any,
        scoreMax: undefined as any,
    });

    const levelData = ref<number[]>([]);
    const dimensionList = ref<DimensionItem[]>([]);

    // 组件数据
    const keyPotentialQualityData = ref<ApiConfigItem[]>([]);
    const jobQualityModelData = ref<ApiConfigItem[]>([]);
    const teamRoleData = ref<ApiConfigItem[]>([]);

    // 表单状态
    const formState = reactive<FormState>({
        loading: false,
        saving: false,
        validating: false,
        errors: {},
    });

    // 验证状态
    const weightValidationState = reactive<WeightValidationState>({
        hasError: false,
        errorGroups: [],
        errorMessage: '',
    });

    // 其他状态
    const currentTab = ref(1);
    const dataChanged = ref(false);
    const saved = ref(false);

    // 计算属性
    const isFormValid = computed(() => {
        return !formState.validating && Object.keys(formState.errors).length === 0 && !weightValidationState.hasError;
    });

    const hasUnsavedChanges = computed(() => {
        return dataChanged.value && !saved.value;
    });

    // Actions

    /**
     * 加载维度数据
     */
    async function loadDimensionList(productId?: number) {
        try {
            formState.loading = true;
            const response = await getDimensionQuery({
                productId: productId || CHARACTER2_CONSTANTS.PRODUCT_ID,
                dimensionLevel: CHARACTER2_CONSTANTS.DIMENSION_LEVEL,
                flat: true,
            });

            if (response.code === 0) {
                dimensionList.value = response.data || [];
            } else {
                throw new Error(response.message || '获取维度数据失败');
            }
        } catch (error) {
            handleApiError(error);
        } finally {
            formState.loading = false;
        }
    }

    /**
     * 加载详情数据
     */
    async function loadDetailData(productId: string | number) {
        try {
            formState.loading = true;
            const response = await getScoreParamDetail({ productId });

            if (response.code === 0 && response.data) {
                const { paramA, paramB, paramC } = response.data;

                // 更新基础参数
                baseParams.value = {
                    paramA,
                    paramB,
                    scoreMin: paramC.scoreMin,
                    scoreMax: paramC.scoreMax,
                };

                // 更新其他数据
                levelData.value = paramC.levelArray || [];
                keyPotentialQualityData.value = paramC.keyPotentialQualityList || [];
                jobQualityModelData.value = paramC.positionQualityModelMatchList || [];
                teamRoleData.value = paramC.teamRoleList || [];

                // 重置状态
                dataChanged.value = false;
                saved.value = false;
                clearErrors();
            } else {
                throw new Error(response.message || '获取详情数据失败');
            }
        } catch (error) {
            handleApiError(error);
        } finally {
            formState.loading = false;
        }
    }

    /**
     * 保存数据
     */
    async function saveData(productId: string | number) {
        try {
            formState.saving = true;

            const params = {
                productId,
                paramA: baseParams.value.paramA,
                paramB: baseParams.value.paramB,
                paramC: {
                    scoreMin: baseParams.value.scoreMin,
                    scoreMax: baseParams.value.scoreMax,
                    levelArray: levelData.value,
                    keyPotentialQualityList: keyPotentialQualityData.value,
                    positionQualityModelMatchList: jobQualityModelData.value,
                    teamRoleList: teamRoleData.value,
                },
            };

            const response = await saveScoreParam(params);

            if (response.code === 0) {
                saved.value = true;
                dataChanged.value = false;
                clearErrors();
                return true;
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            handleApiError(error);
            return false;
        } finally {
            formState.saving = false;
        }
    }

    /**
     * 更新基础参数
     */
    function updateBaseParams(params: Partial<Character2BaseParams>) {
        Object.assign(baseParams.value, params);
        markDataChanged();
    }

    /**
     * 更新等级数据
     */
    function updateLevelData(data: number[]) {
        levelData.value = [...data];
        markDataChanged();
    }

    /**
     * 更新关键潜在素质数据
     */
    function updateKeyPotentialQualityData(data: ApiConfigItem[]) {
        keyPotentialQualityData.value = [...data];
        markDataChanged();
    }

    /**
     * 更新岗位素质模型数据
     */
    function updateJobQualityModelData(data: ApiConfigItem[]) {
        jobQualityModelData.value = [...data];
        markDataChanged();
    }

    /**
     * 更新团队角色数据
     */
    function updateTeamRoleData(data: ApiConfigItem[]) {
        teamRoleData.value = [...data];
        markDataChanged();
    }

    /**
     * 验证权重和
     */
    function validateWeights(configGroups: any[], weightTableData: any[]) {
        const result = validateWeightSum(configGroups, weightTableData);

        weightValidationState.hasError = result.hasError;
        weightValidationState.errorGroups = result.errorGroups;
        weightValidationState.errorMessage = result.hasError ? `配置组 ${result.errorGroups.join(', ')} 的权重和必须等于 1` : '';
    }

    /**
     * 设置表单错误
     */
    function setFormError(field: string, message: string) {
        formState.errors[field] = message;
    }

    /**
     * 清除表单错误
     */
    function clearFormError(field: string) {
        delete formState.errors[field];
    }

    /**
     * 清除所有错误
     */
    function clearErrors() {
        formState.errors = {};
        weightValidationState.hasError = false;
        weightValidationState.errorGroups = [];
        weightValidationState.errorMessage = '';
    }

    /**
     * 标记数据已更改
     */
    function markDataChanged() {
        dataChanged.value = true;
        saved.value = false;
    }

    /**
     * 重置表单
     */
    function resetForm() {
        baseParams.value = {
            paramA: undefined as any,
            paramB: undefined as any,
            scoreMin: undefined as any,
            scoreMax: undefined as any,
        };
        levelData.value = [];
        keyPotentialQualityData.value = [];
        jobQualityModelData.value = [];
        teamRoleData.value = [];
        dimensionList.value = [];

        dataChanged.value = false;
        saved.value = false;
        currentTab.value = 1;

        clearErrors();
    }

    /**
     * 设置当前标签页
     */
    function setCurrentTab(tab: number) {
        currentTab.value = tab;
    }

    return {
        // 状态
        baseParams,
        levelData,
        dimensionList,
        keyPotentialQualityData,
        jobQualityModelData,
        teamRoleData,
        formState,
        weightValidationState,
        currentTab,
        dataChanged,
        saved,

        // 计算属性
        isFormValid,
        hasUnsavedChanges,

        // 方法
        loadDimensionList,
        loadDetailData,
        saveData,
        updateBaseParams,
        updateLevelData,
        updateKeyPotentialQualityData,
        updateJobQualityModelData,
        updateTeamRoleData,
        validateWeights,
        setFormError,
        clearFormError,
        clearErrors,
        markDataChanged,
        resetForm,
        setCurrentTab,
    };
});
