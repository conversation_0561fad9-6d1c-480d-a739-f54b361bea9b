/**
 * 五维性格测评 2.0 统一组合式函数
 * 合并了状态管理、表单逻辑和验证逻辑
 */

import { ref, computed, watch, nextTick, type Ref } from 'vue';
import { useRoute } from 'vue-router';
import type { _FormComponent } from '@boss/design';
import { useFormValidator } from '@crm/vueuse-pro';
import type { ConfigGroup, WeightTableRow, NormTableRow, TableColumn, ComponentFormData, ApiConfigItem, ValidationRule } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { useCharacter2Store } from '../store';

export interface UseCharacter2Options {
    formRef?: Ref<_FormComponent | undefined>;
    tableType?: 'key-potential' | 'job-quality' | 'team-role';
}

export function useCharacter2(options: UseCharacter2Options = {}) {
    const { formRef, tableType = 'key-potential' } = options;
    const route = useRoute();
    const store = useCharacter2Store();

    // ==================== 内部状态 ====================
    const configGroups = ref<ConfigGroup[]>([{ id: '1', name: '配置组1' }]);
    const weightTableData = ref<WeightTableRow[]>([]);
    const normTableData = ref<NormTableRow[]>([]);
    const weightColumns = ref<TableColumn[]>([]);
    const normColumns = ref<TableColumn[]>([]);
    const weightSumError = ref(false);

    // ==================== 计算属性 ====================
    const productId = computed(() => route.query?.productId || CHARACTER2_CONSTANTS.PRODUCT_ID);
    const canAddConfigGroup = computed(() => configGroups.value.length < CHARACTER2_CONSTANTS.MAX_CONFIG_GROUPS);
    const canRemoveConfigGroup = computed(() => configGroups.value.length > 1);

    // ==================== 表单验证器 ====================
    const { validateAll } = formRef
        ? useFormValidator({
              formRef,
              customValidate: async () => {
                  return !store.weightValidationState.hasError;
              },
          })
        : { validateAll: async () => true };

    // ==================== 验证规则 ====================
    const baseParamRules = computed(
        (): Record<string, ValidationRule[]> => ({
            paramA: [
                { required: true, type: 'number', message: '请填写参数a' },
                { validator: validateParamRange, message: '参数a必须在1-99范围内' },
            ],
            paramB: [
                { required: true, type: 'number', message: '请填写参数b' },
                { validator: validateParamRange, message: '参数b必须在1-99范围内' },
            ],
            scoreMin: [
                { required: true, type: 'number', message: '请填写得分下限' },
                { validator: validateScoreMin, message: '得分下限必须在1-99范围内且小于得分上限' },
            ],
            scoreMax: [
                { required: true, type: 'number', message: '请填写得分上限' },
                { validator: validateScoreMax, message: '得分上限必须在1-99范围内且大于得分下限' },
            ],
        })
    );

    const levelRules = computed(() => {
        const rules: Record<string, ValidationRule[]> = {};
        for (let i = 0; i < 4; i++) {
            rules[`levels.${i}`] = [
                { required: true, message: '请填写等级划分' },
                { validator: (value, callback) => validateLevelValue(value, callback, i), message: '等级数值无效' },
            ];
        }
        return rules;
    });

    // ==================== 表单操作方法 ====================

    /**
     * 初始化表格数据
     */
    function initTableData() {
        if (store.dimensionList.length === 0) return;

        // 初始化权重表数据
        weightTableData.value = store.dimensionList.map((dimension) => {
            const row: WeightTableRow = {
                dimensionName: dimension.name,
                encryptDimensionId: dimension.encryptId,
            };

            // 为每个配置组添加权重字段
            configGroups.value.forEach((group) => {
                row[`weight_${group.id}`] = 0;
            });

            return row;
        });

        // 初始化常模表数据
        normTableData.value = configGroups.value.map((group) => ({
            groupId: group.id,
            groupName: group.name,
            avgScore: 0,
            stdDev: 0,
        }));

        updateColumns();
    }

    /**
     * 初始化团队角色配置组
     */
    function initTeamRoleConfigGroups() {
        const teamRoles = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES;
        configGroups.value = teamRoles.map((role, index) => ({
            id: String(index + 1),
            name: role,
        }));

        if (store.dimensionList.length > 0) {
            initTableData();
        }
    }

    /**
     * 更新列配置
     */
    function updateColumns() {
        // 更新权重表列配置
        weightColumns.value = [
            {
                label: '二级维度',
                field: 'dimension',
                width: 200,
            },
            ...configGroups.value.map((group) => ({
                label: group.name,
                field: `weight_${group.id}`,
                width: 160,
            })),
        ];

        // 更新常模表列配置
        let firstColumnLabel = '关键潜在素质';
        if (tableType === 'job-quality') {
            firstColumnLabel = '岗位素质模型';
        } else if (tableType === 'team-role') {
            firstColumnLabel = '团队角色';
        }

        normColumns.value = [
            {
                label: firstColumnLabel,
                field: 'groupName',
                width: 200,
            },
            {
                label: '常模平均分',
                field: 'avgScore',
                width: 150,
            },
            {
                label: '常模标准差',
                field: 'stdDev',
                width: 150,
            },
        ];
    }

    /**
     * 添加配置组
     */
    function addConfigGroup() {
        if (!canAddConfigGroup.value) return;

        const newId = String(configGroups.value.length + 1);
        const newGroup: ConfigGroup = {
            id: newId,
            name: `配置组${newId}`,
        };

        configGroups.value.push(newGroup);

        // 为权重表添加新列
        weightTableData.value.forEach((row) => {
            row[`weight_${newId}`] = 0;
        });

        // 为常模表添加新行
        normTableData.value.push({
            groupId: newId,
            groupName: newGroup.name,
            avgScore: 0,
            stdDev: 0,
        });

        updateColumns();
        validateWeightSum();
    }

    /**
     * 删除配置组
     */
    function removeConfigGroup(groupId: string) {
        if (!canRemoveConfigGroup.value) return;

        // 删除配置组
        configGroups.value = configGroups.value.filter((group) => group.id !== groupId);

        // 删除权重表对应列
        weightTableData.value.forEach((row) => {
            delete row[`weight_${groupId}`];
        });

        // 删除常模表对应行
        normTableData.value = normTableData.value.filter((row) => row.groupId !== groupId);

        updateColumns();
        validateWeightSum();
    }

    // ==================== 验证方法 ====================

    /**
     * 验证权重和
     */
    function validateWeightSum() {
        const errorGroups: string[] = [];
        let hasError = false;

        configGroups.value.forEach((group) => {
            const weightSum = weightTableData.value.reduce((sum, row) => {
                const weight = Number(row[`weight_${group.id}`]) || 0;
                return sum + weight;
            }, 0);

            const roundedSum = Math.round(weightSum * 10000) / 10000;
            if (Math.abs(roundedSum - 1) > 0.0001) {
                errorGroups.push(group.name);
                hasError = true;
            }
        });

        store.weightValidationState.hasError = hasError;
        store.weightValidationState.errorGroups = errorGroups;
        store.weightValidationState.errorMessage = hasError ? `以下配置组权重和不等于1: ${errorGroups.join(', ')}` : '';

        weightSumError.value = hasError;
        return !hasError;
    }

    /**
     * 验证参数范围
     */
    function validateParamRange(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请填写参数');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.PARAM_MIN || value > CHARACTER2_CONSTANTS.PARAM_MAX) {
            callback(`参数必须在${CHARACTER2_CONSTANTS.PARAM_MIN}-${CHARACTER2_CONSTANTS.PARAM_MAX}范围内`);
            return;
        }

        callback();
    }

    /**
     * 验证得分下限
     */
    function validateScoreMin(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请填写得分下限');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.SCORE_MIN || value > CHARACTER2_CONSTANTS.SCORE_MAX) {
            callback(`得分下限必须在${CHARACTER2_CONSTANTS.SCORE_MIN}-${CHARACTER2_CONSTANTS.SCORE_MAX}范围内`);
            return;
        }

        if (store.baseParams.scoreMax !== undefined && value >= store.baseParams.scoreMax) {
            callback('得分下限必须小于得分上限');
            return;
        }

        callback();
    }

    /**
     * 验证得分上限
     */
    function validateScoreMax(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请填写得分上限');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.SCORE_MIN || value > CHARACTER2_CONSTANTS.SCORE_MAX) {
            callback(`得分上限必须在${CHARACTER2_CONSTANTS.SCORE_MIN}-${CHARACTER2_CONSTANTS.SCORE_MAX}范围内`);
            return;
        }

        if (store.baseParams.scoreMin !== undefined && value <= store.baseParams.scoreMin) {
            callback('得分上限必须大于得分下限');
            return;
        }

        callback();
    }

    /**
     * 验证等级数值
     */
    function validateLevelValue(value: number, callback: (message?: string) => void, index: number) {
        if (value === undefined || value === null) {
            callback('请填写等级划分');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.LEVEL_MIN || value > CHARACTER2_CONSTANTS.LEVEL_MAX) {
            callback(`等级数值必须在${CHARACTER2_CONSTANTS.LEVEL_MIN}-${CHARACTER2_CONSTANTS.LEVEL_MAX}范围内`);
            return;
        }

        // 验证递增性
        if (index < store.levelData.length - 1) {
            const nextValue = store.levelData[index + 1];
            if (nextValue !== undefined && value >= nextValue) {
                callback('数值必须小于右侧值');
                return;
            }
        }

        callback();
    }

    /**
     * 验证权重值
     */
    function validateWeightValue(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请输入权重');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.WEIGHT_MIN || value > CHARACTER2_CONSTANTS.WEIGHT_MAX) {
            callback(`权重必须在${CHARACTER2_CONSTANTS.WEIGHT_MIN}-${CHARACTER2_CONSTANTS.WEIGHT_MAX}范围内`);
            return;
        }

        callback();
    }

    /**
     * 验证常模平均分
     */
    function validateNormAvgScore(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请输入常模平均分');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.NORM_MIN || value > CHARACTER2_CONSTANTS.NORM_MAX) {
            callback(`常模平均分必须在${CHARACTER2_CONSTANTS.NORM_MIN}-${CHARACTER2_CONSTANTS.NORM_MAX}范围内`);
            return;
        }

        callback();
    }

    /**
     * 验证常模标准差
     */
    function validateNormStdDev(value: number, callback: (message?: string) => void) {
        if (value === undefined || value === null) {
            callback('请输入常模标准差');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN) {
            callback(`常模标准差必须大于${CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN}`);
            return;
        }

        callback();
    }

    /**
     * 验证配置组名称
     */
    function validateConfigGroupName(value: string, callback: (message?: string) => void) {
        if (!value || value.trim() === '') {
            callback('请输入配置组名称');
            return;
        }

        if (value.length > 50) {
            callback('配置组名称不能超过50个字符');
            return;
        }

        callback();
    }

    /**
     * 验证团队角色名称
     */
    function validateTeamRoleName(value: string, callback: (message?: string) => void) {
        if (!value || value.trim() === '') {
            callback('请输入团队角色名称');
            return;
        }

        if (value.length > 20) {
            callback('团队角色名称不能超过20个字符');
            return;
        }

        callback();
    }

    /**
     * 获取权重验证规则
     */
    function getWeightRules(_rowIndex?: number, _field?: string): ValidationRule[] {
        return [
            { required: true, message: '请输入权重' },
            { validator: validateWeightValue, message: '权重必须在0-1范围内' },
        ];
    }

    /**
     * 获取常模验证规则
     */
    function getNormRules(field: string): ValidationRule[] {
        if (field === 'avgScore') {
            return [
                { required: true, message: '请输入常模平均分' },
                { validator: validateNormAvgScore, message: '常模平均分必须在0-1000范围内' },
            ];
        } else if (field === 'stdDev') {
            return [
                { required: true, message: '请输入常模标准差' },
                { validator: validateNormStdDev, message: '常模标准差必须大于0.0001' },
            ];
        }
        return [];
    }

    /**
     * 获取配置组名称验证规则
     */
    function getConfigGroupNameRules(): ValidationRule[] {
        return [
            { required: true, message: '请输入配置组名称' },
            { validator: validateConfigGroupName, message: '配置组名称格式不正确' },
        ];
    }

    /**
     * 获取团队角色名称验证规则
     */
    function getTeamRoleNameRules(): ValidationRule[] {
        return [
            { required: true, message: '请输入团队角色名称' },
            { validator: validateTeamRoleName, message: '团队角色名称格式不正确' },
        ];
    }

    /**
     * 获取团队角色权重验证规则
     */
    function getTeamRoleWeightRules(): ValidationRule[] {
        return [
            { required: true, message: '请输入权重' },
            { validator: validateWeightValue, message: '权重必须在0-1范围内' },
        ];
    }

    /**
     * 验证整个表单
     */
    async function validateForm(): Promise<boolean> {
        try {
            store.formState.validating = true;

            // 验证基础表单
            const isValid = await validateAll();

            // 验证权重和
            if (isValid && store.weightValidationState.hasError) {
                store.setFormError('weightSum', store.weightValidationState.errorMessage);
                return false;
            }

            if (isValid) {
                store.clearErrors();
            }

            return isValid;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('表单验证失败:', error);
            return false;
        } finally {
            store.formState.validating = false;
        }
    }

    /**
     * 验证特定字段
     */
    async function validateField(field: string): Promise<boolean> {
        try {
            if (formRef?.value) {
                await formRef.value.validateField([field]);
                store.clearFormError(field);
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 从API数据初始化表单
     */
    function initFromApiData(apiData: ApiConfigItem[]) {
        try {
            if (!apiData || apiData.length === 0) {
                resetFormData();
                return;
            }

            // 提取配置组
            const groups: ConfigGroup[] = apiData.map((item, index) => ({
                id: item.encId || String(index + 1),
                name: item.headName || `配置组${index + 1}`,
            }));

            // 构建权重表数据
            const weightData: WeightTableRow[] = [];
            if (store.dimensionList.length > 0) {
                store.dimensionList.forEach((dimension) => {
                    const row: WeightTableRow = {
                        dimensionName: dimension.name,
                        encryptDimensionId: dimension.encryptId,
                    };

                    // 为每个配置组设置权重值
                    groups.forEach((group) => {
                        const apiItem = apiData.find((item) => (item.encId || '') === group.id);
                        const rowData = apiItem?.rowDataList?.find((r) => r.encDimensionId === dimension.encryptId);
                        row[`weight_${group.id}`] = rowData?.weight || 0;
                    });

                    weightData.push(row);
                });
            }

            // 构建常模表数据
            const normData: NormTableRow[] = groups.map((group) => {
                const apiItem = apiData.find((item) => (item.encId || '') === group.id);
                return {
                    groupId: group.id,
                    groupName: group.name,
                    avgScore: apiItem?.normalAverageScore || 0,
                    stdDev: apiItem?.normalStandardDeviation || 0,
                };
            });

            // 更新内部状态
            if (groups.length > 0) {
                configGroups.value = groups;
                weightTableData.value = weightData;
                normTableData.value = normData;
                updateColumns();

                // 强制触发响应式更新
                nextTick(() => {
                    validateWeightSum();
                });
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('从API数据初始化失败:', error);
        }
    }

    /**
     * 获取当前表单数据
     */
    function getCurrentFormData(): ComponentFormData {
        return {
            configGroups: configGroups.value,
            weightTableData: weightTableData.value,
            normTableData: normTableData.value,
            dimensionList: store.dimensionList,
        };
    }

    /**
     * 重置表单数据
     */
    function resetFormData() {
        configGroups.value = [{ id: '1', name: '配置组1' }];
        weightTableData.value = [];
        normTableData.value = [];
        weightSumError.value = false;
        initTableData();
    }

    // 监听配置组名称变化，同步到常模表
    watch(
        () => configGroups.value.map((group) => ({ id: group.id, name: group.name })),
        (newGroups) => {
            newGroups.forEach((group) => {
                const normItem = normTableData.value.find((item) => item.groupId === group.id);
                if (normItem) {
                    normItem.groupName = group.name;
                }
            });
        },
        { deep: true }
    );

    // 监听维度数据变化，重新初始化表格
    watch(
        () => store.dimensionList,
        (newDimensionList) => {
            if (newDimensionList.length > 0) {
                initTableData();
            }
        },
        { immediate: true }
    );

    return {
        // Store 状态（直接暴露）
        baseParams: store.baseParams,
        levelData: store.levelData,
        dimensionList: store.dimensionList,
        keyPotentialQualityData: store.keyPotentialQualityData,
        jobQualityModelData: store.jobQualityModelData,
        teamRoleData: store.teamRoleData,
        formState: store.formState,
        weightValidationState: store.weightValidationState,
        currentTab: store.currentTab,
        dataChanged: store.dataChanged,
        saved: store.saved,

        // 表单状态
        configGroups,
        weightTableData,
        normTableData,
        weightColumns,
        normColumns,
        weightSumError,

        // 计算属性
        productId,
        canAddConfigGroup,
        canRemoveConfigGroup,
        isFormValid: store.isFormValid,
        hasUnsavedChanges: store.hasUnsavedChanges,

        // Store 方法
        loadDimensionList: store.loadDimensionList,
        loadDetailData: store.loadDetailData,
        saveData: store.saveData,
        updateBaseParams: store.updateBaseParams,
        updateLevelData: store.updateLevelData,
        updateKeyPotentialQualityData: store.updateKeyPotentialQualityData,
        updateJobQualityModelData: store.updateJobQualityModelData,
        updateTeamRoleData: store.updateTeamRoleData,
        validateWeights: store.validateWeights,
        setFormError: store.setFormError,
        clearFormError: store.clearFormError,
        clearErrors: store.clearErrors,
        markDataChanged: store.markDataChanged,
        resetForm: store.resetForm,
        setCurrentTab: store.setCurrentTab,

        // 表单操作方法
        initTableData,
        initTeamRoleConfigGroups,
        updateColumns,
        addConfigGroup,
        removeConfigGroup,
        validateWeightSum,
        initFromApiData,
        getCurrentFormData,
        resetFormData,

        // 验证规则
        baseParamRules,
        levelRules,
        getWeightRules,
        getNormRules,
        getConfigGroupNameRules,
        getTeamRoleNameRules,
        getTeamRoleWeightRules,

        // 验证方法
        validateForm,
        validateField,
        validateAll,
        validateParamRange,
        validateScoreMin,
        validateScoreMax,
        validateLevelValue,
        validateWeightValue,
        validateNormAvgScore,
        validateNormStdDev,
        validateConfigGroupName,
        validateTeamRoleName,
    };
}
