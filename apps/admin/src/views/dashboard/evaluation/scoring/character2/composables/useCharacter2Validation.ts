/**
 * 五维性格测评 2.0 表单验证组合式函数
 */

import { ref, computed, type Ref } from 'vue';
import type { _FormComponent } from '@boss/design';
import { useFormValidator } from '@crm/vueuse-pro';
import type { ValidationRule } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { useCharacter2Store } from '../store';

export function useCharacter2Validation(formRef: Ref<_FormComponent | undefined>) {
    const store = useCharacter2Store();

    // 表单验证器
    const { validateAll } = useFormValidator({
        formRef,
        customValidate: async () => {
            // 自定义校验逻辑
            return !store.weightValidationState.hasError;
        },
    });

    /**
     * 基础参数验证规则
     */
    const baseParamRules = computed(
        (): Record<string, ValidationRule[]> => ({
            paramA: [
                { required: true, type: 'number', message: '请填写参数a' },
                { validator: validateParamRange, message: '参数a必须在1-99范围内' },
            ],
            paramB: [
                { required: true, type: 'number', message: '请填写参数b' },
                { validator: validateParamRange, message: '参数b必须在1-99范围内' },
            ],
            scoreMin: [
                { required: true, type: 'number', message: '请填写得分下限' },
                { validator: validateScoreMin, message: '得分下限必须在1-99范围内且小于得分上限' },
            ],
            scoreMax: [
                { required: true, type: 'number', message: '请填写得分上限' },
                { validator: validateScoreMax, message: '得分上限必须在1-99范围内且大于得分下限' },
            ],
        })
    );

    /**
     * 等级划分验证规则
     */
    const levelRules = computed(() => {
        const rules: Record<string, ValidationRule[]> = {};

        for (let i = 0; i < 4; i++) {
            // 只有前4个等级需要验证
            rules[`levels.${i}`] = [
                { required: true, message: '请填写等级划分' },
                { validator: (value, callback) => validateLevelValue(value, callback, i), message: '等级数值无效' },
            ];
        }

        return rules;
    });

    /**
     * 权重表验证规则
     */
    function getWeightRules(rowIndex: number, field: string): ValidationRule[] {
        return [
            { required: true, message: '请输入权重' },
            { validator: validateWeightValue, message: '权重必须在0-1范围内' },
        ];
    }

    /**
     * 常模表验证规则
     */
    function getNormRules(field: 'avgScore' | 'stdDev'): ValidationRule[] {
        if (field === 'avgScore') {
            return [
                { required: true, message: '请填写常模平均分' },
                { validator: validateNormAvgScore, message: '常模平均分必须在0-1000范围内' },
            ];
        } else {
            return [
                { required: true, message: '请填写常模标准差' },
                { validator: validateNormStdDev, message: '常模标准差必须在0.0001-1000范围内' },
            ];
        }
    }

    /**
     * 配置组名称验证规则
     */
    function getConfigGroupNameRules(): ValidationRule[] {
        return [
            { required: true, message: '请填写配置组名称' },
            { validator: validateConfigGroupName, message: '配置组名称不能超过10个字符' },
        ];
    }

    /**
     * 团队角色名称验证规则
     */
    function getTeamRoleNameRules(): ValidationRule[] {
        return [
            { required: true, message: '请输入团队角色名称' },
            { validator: validateTeamRoleName, message: '团队角色名称不能为空' },
        ];
    }

    /**
     * 团队角色权重验证规则
     */
    function getTeamRoleWeightRules(): ValidationRule[] {
        return [
            { required: true, message: '请输入权重' },
            { validator: validateWeightValue, message: '权重必须在0-1范围内' },
        ];
    }

    // 验证函数

    /**
     * 验证参数范围
     */
    function validateParamRange(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.PARAM_MIN || value > CHARACTER2_CONSTANTS.PARAM_MAX) {
                callback(`参数必须在${CHARACTER2_CONSTANTS.PARAM_MIN}-${CHARACTER2_CONSTANTS.PARAM_MAX}范围内`);
                return;
            }
        }
        callback();
    }

    /**
     * 验证得分下限
     */
    function validateScoreMin(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.SCORE_MIN || value > CHARACTER2_CONSTANTS.SCORE_MAX) {
                callback(`得分下限必须在${CHARACTER2_CONSTANTS.SCORE_MIN}-${CHARACTER2_CONSTANTS.SCORE_MAX}范围内`);
                return;
            }

            if (store.baseParams.scoreMax !== undefined && value >= store.baseParams.scoreMax) {
                callback('得分下限必须小于得分上限');
                return;
            }
        }
        callback();
    }

    /**
     * 验证得分上限
     */
    function validateScoreMax(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.SCORE_MIN || value > CHARACTER2_CONSTANTS.SCORE_MAX) {
                callback(`得分上限必须在${CHARACTER2_CONSTANTS.SCORE_MIN}-${CHARACTER2_CONSTANTS.SCORE_MAX}范围内`);
                return;
            }

            if (store.baseParams.scoreMin !== undefined && value <= store.baseParams.scoreMin) {
                callback('得分上限必须大于得分下限');
                return;
            }
        }
        callback();
    }

    /**
     * 验证等级数值
     */
    function validateLevelValue(value: number, callback: (message?: string) => void, index: number) {
        if (value === undefined || value === null) {
            callback('请填写等级划分');
            return;
        }

        if (value < CHARACTER2_CONSTANTS.LEVEL_MIN || value > CHARACTER2_CONSTANTS.LEVEL_MAX) {
            callback(`等级数值必须在${CHARACTER2_CONSTANTS.LEVEL_MIN}-${CHARACTER2_CONSTANTS.LEVEL_MAX}范围内`);
            return;
        }

        // 验证递增性
        if (index < store.levelData.length - 1) {
            const nextValue = store.levelData[index + 1];
            if (nextValue !== undefined && value >= nextValue) {
                callback('数值必须小于右侧值');
                return;
            }
        }

        callback();
    }

    /**
     * 验证权重值
     */
    function validateWeightValue(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.WEIGHT_MIN || value > CHARACTER2_CONSTANTS.WEIGHT_MAX) {
                callback(`权重必须在${CHARACTER2_CONSTANTS.WEIGHT_MIN}-${CHARACTER2_CONSTANTS.WEIGHT_MAX}范围内`);
                return;
            }
        }
        callback();
    }

    /**
     * 验证常模平均分
     */
    function validateNormAvgScore(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.NORM_MIN || value > CHARACTER2_CONSTANTS.NORM_MAX) {
                callback(`常模平均分必须在${CHARACTER2_CONSTANTS.NORM_MIN}-${CHARACTER2_CONSTANTS.NORM_MAX}范围内`);
                return;
            }
        }
        callback();
    }

    /**
     * 验证常模标准差
     */
    function validateNormStdDev(value: number, callback: (message?: string) => void) {
        if (value !== undefined && value !== null) {
            if (value < CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN || value > CHARACTER2_CONSTANTS.NORM_MAX) {
                callback(`常模标准差必须在${CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN}-${CHARACTER2_CONSTANTS.NORM_MAX}范围内`);
                return;
            }
        }
        callback();
    }

    /**
     * 验证配置组名称
     */
    function validateConfigGroupName(value: string, callback: (message?: string) => void) {
        if (value && value.length > 10) {
            callback('配置组名称不能超过10个字符');
            return;
        }
        callback();
    }

    /**
     * 验证团队角色名称
     */
    function validateTeamRoleName(value: string, callback: (message?: string) => void) {
        if (!value || value.trim().length === 0) {
            callback('团队角色名称不能为空');
            return;
        }
        callback();
    }

    /**
     * 验证整个表单
     */
    async function validateForm(): Promise<boolean> {
        try {
            store.formState.validating = true;

            // 验证基础表单
            const isValid = await validateAll();

            // 验证权重和
            if (isValid && store.weightValidationState.hasError) {
                store.setFormError('weightSum', store.weightValidationState.errorMessage);
                return false;
            }

            if (isValid) {
                store.clearErrors();
            }

            return isValid;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('表单验证失败:', error);
            return false;
        } finally {
            store.formState.validating = false;
        }
    }

    /**
     * 验证特定字段
     */
    async function validateField(field: string): Promise<boolean> {
        try {
            if (formRef.value) {
                await formRef.value.validateField([field]);
                store.clearFormError(field);
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    return {
        // 验证规则
        baseParamRules,
        levelRules,
        getWeightRules,
        getNormRules,
        getConfigGroupNameRules,
        getTeamRoleNameRules,
        getTeamRoleWeightRules,

        // 验证方法
        validateForm,
        validateField,
        validateAll,

        // 单独的验证函数
        validateParamRange,
        validateScoreMin,
        validateScoreMax,
        validateLevelValue,
        validateWeightValue,
        validateNormAvgScore,
        validateNormStdDev,
        validateConfigGroupName,
        validateTeamRoleName,
    };
}
